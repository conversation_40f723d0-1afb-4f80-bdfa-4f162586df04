#!/usr/bin/env node

/**
 * 简单的认证系统测试
 * 直接测试认证模块，不依赖Complete Core
 */

async function testAuthSystem() {
  console.log('🔐 开始测试自定义认证系统...\n');

  try {
    // 从构建的输出文件中导入认证模块
    const authModule = require('./out/index.js');
    const { AuthSystemFactory, PasswordUtils } = authModule;

    console.log('✅ 认证模块导入成功');

    // 测试密码工具
    console.log('\n1. 测试密码工具...');
    
    const password = 'TestPassword123';
    const hashedPassword = await PasswordUtils.hashPassword(password);
    console.log('   ✅ 密码哈希生成成功');
    
    const isValid = await PasswordUtils.verifyPassword(password, hashedPassword);
    console.log(`   ${isValid ? '✅' : '❌'} 密码验证: ${isValid ? '通过' : '失败'}`);
    
    const policy = {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false
    };
    
    const strengthResult = PasswordUtils.validatePasswordStrength(password, policy);
    console.log(`   ${strengthResult.isValid ? '✅' : '❌'} 密码强度验证: ${strengthResult.isValid ? '通过' : '失败'} (分数: ${strengthResult.score})`);

    // 测试认证系统创建
    console.log('\n2. 测试认证系统创建...');
    
    const authSystem = await AuthSystemFactory.createAuthSystem({
      storageType: 'memory',
      authConfig: {
        authType: 'local',
        tokenExpiration: 3600,
        passwordPolicy: policy
      }
    });
    
    console.log('   ✅ 认证系统创建成功');
    console.log('   ✅ 认证管理器已初始化');
    console.log('   ✅ 用户管理器已初始化');
    console.log('   ✅ 令牌管理器已初始化');
    console.log('   ✅ 存储系统已初始化');

    // 测试用户创建
    console.log('\n3. 测试用户管理...');
    
    const testUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestUser123',
      firstName: 'Test',
      lastName: 'User'
    };
    
    const createdUser = await authSystem.userManager.createUser(testUser);
    console.log(`   ✅ 用户创建成功: ${createdUser.username} (ID: ${createdUser.id})`);
    
    // 测试用户认证
    console.log('\n4. 测试用户认证...');
    
    const authResult = await authSystem.authManager.authenticate({
      username: testUser.username,
      password: testUser.password
    });
    
    if (authResult.success) {
      console.log('   ✅ 用户认证成功');
      console.log(`   ✅ 访问令牌已生成: ${authResult.token.accessToken.substring(0, 20)}...`);
      console.log(`   ✅ 会话已创建: ${authResult.sessionInfo.sessionId}`);
      
      // 测试令牌验证
      console.log('\n5. 测试令牌验证...');
      
      const tokenValidation = await authSystem.authManager.validateToken(authResult.token.accessToken);
      console.log(`   ${tokenValidation.isValid ? '✅' : '❌'} 令牌验证: ${tokenValidation.isValid ? '有效' : '无效'}`);
      
      if (tokenValidation.user) {
        console.log(`   ✅ 用户信息提取成功: ${tokenValidation.user.username}`);
      }
      
      // 测试登出
      console.log('\n6. 测试用户登出...');
      
      const logoutResult = await authSystem.authManager.logout(authResult.sessionInfo.sessionId);
      console.log(`   ${logoutResult ? '✅' : '❌'} 用户登出: ${logoutResult ? '成功' : '失败'}`);
      
    } else {
      console.log(`   ❌ 用户认证失败: ${authResult.error}`);
    }

    console.log('\n🎉 认证系统测试完成！');
    console.log('\n📊 测试结果总结:');
    console.log('   ✅ 密码工具功能正常');
    console.log('   ✅ 认证系统创建成功');
    console.log('   ✅ 用户管理功能正常');
    console.log('   ✅ 用户认证功能正常');
    console.log('   ✅ 令牌管理功能正常');
    console.log('   ✅ 会话管理功能正常');
    
    console.log('\n🚀 自定义认证系统已准备就绪！');
    console.log('\n📝 默认管理员账户:');
    console.log('   用户名: admin');
    console.log('   密码: Continue@2024');
    
    console.log('\n🔧 下一步操作:');
    console.log('   1. 启动 Continue Core: ./out/index.js');
    console.log('   2. 使用默认管理员账户登录');
    console.log('   3. 测试完整的认证流程');

  } catch (error) {
    console.error('❌ 认证系统测试失败:', error);
    console.error('\n🔍 错误详情:', error.stack);
    process.exit(1);
  }
}

// 运行测试
testAuthSystem().catch(console.error);
