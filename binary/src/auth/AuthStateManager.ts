/**
 * 认证状态管理器
 * 负责管理认证状态的同步和通知
 */

import { CustomSessionInfo, User } from './AuthTypes';
import { AuthCoreIntegrator } from './AuthCoreIntegration';

export class AuthStateManager {
  private currentSession: CustomSessionInfo | null = null;
  private authIntegrator: AuthCoreIntegrator;
  private messenger: any;

  constructor(authIntegrator: AuthCoreIntegrator, messenger: any) {
    this.authIntegrator = authIntegrator;
    this.messenger = messenger;
    
    // 监听认证状态变化
    this.setupAuthEventListeners();
  }

  /**
   * 设置认证事件监听器
   */
  private setupAuthEventListeners(): void {
    // 这里可以监听认证系统的事件
    // 由于我们的认证系统是集成在同一个进程中，可以直接调用方法
  }

  /**
   * 设置当前会话
   */
  setCurrentSession(session: CustomSessionInfo | null): void {
    const previousSession = this.currentSession;
    this.currentSession = session;

    // 通知会话状态变化
    if (session && !previousSession) {
      // 用户登录
      this.notifySessionCreated(session);
    } else if (!session && previousSession) {
      // 用户登出
      this.notifySessionDestroyed(previousSession);
    } else if (session && previousSession && session.sessionId !== previousSession.sessionId) {
      // 会话更新
      this.notifySessionUpdated(session);
    }
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): CustomSessionInfo | null {
    return this.currentSession;
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): User | null {
    return this.currentSession?.user || null;
  }

  /**
   * 检查用户是否已认证
   */
  isAuthenticated(): boolean {
    return this.currentSession !== null && 
           this.currentSession.expiresAt > new Date();
  }

  /**
   * 检查用户是否有指定角色
   */
  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.roles.includes(role) || false;
  }

  /**
   * 检查用户是否为管理员
   */
  isAdmin(): boolean {
    return this.hasRole('admin') || this.hasRole('super_admin');
  }

  /**
   * 通知会话创建
   */
  private notifySessionCreated(session: CustomSessionInfo): void {
    console.log('[AuthStateManager] 用户登录:', session.user.username);
    
    // 发送会话更新事件到GUI
    this.messenger.send("sessionUpdate", {
      sessionInfo: {
        AUTH_TYPE: "custom",
        accessToken: session.token.accessToken,
        refreshToken: session.token.refreshToken,
        user: session.user,
        sessionId: session.sessionId,
        expiresAt: session.expiresAt
      }
    });

    // 发送配置更新事件（可能需要根据用户权限更新配置）
    this.messenger.send("configUpdate", {
      result: null, // 这里可以传递更新后的配置
      profileId: null,
      organizations: [],
      selectedOrgId: null
    });
  }

  /**
   * 通知会话销毁
   */
  private notifySessionDestroyed(session: CustomSessionInfo): void {
    console.log('[AuthStateManager] 用户登出:', session.user.username);
    
    // 发送会话更新事件到GUI
    this.messenger.send("sessionUpdate", {
      sessionInfo: null
    });
  }

  /**
   * 通知会话更新
   */
  private notifySessionUpdated(session: CustomSessionInfo): void {
    console.log('[AuthStateManager] 会话更新:', session.user.username);
    
    // 发送会话更新事件到GUI
    this.messenger.send("sessionUpdate", {
      sessionInfo: {
        AUTH_TYPE: "custom",
        accessToken: session.token.accessToken,
        refreshToken: session.token.refreshToken,
        user: session.user,
        sessionId: session.sessionId,
        expiresAt: session.expiresAt
      }
    });
  }

  /**
   * 处理令牌刷新
   */
  async handleTokenRefresh(): Promise<boolean> {
    if (!this.currentSession) {
      return false;
    }

    try {
      const refreshResponse = await this.authIntegrator['authManager'].refreshToken({
        refreshToken: this.currentSession.token.refreshToken
      });

      if (refreshResponse.success && refreshResponse.token) {
        // 更新当前会话的令牌
        this.currentSession.token = refreshResponse.token;
        this.currentSession.expiresAt = refreshResponse.token.expiresAt;
        
        // 通知令牌更新
        this.notifyTokenRefreshed(refreshResponse.token.accessToken);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('[AuthStateManager] 令牌刷新失败:', error);
      return false;
    }
  }

  /**
   * 通知令牌刷新
   */
  private notifyTokenRefreshed(newToken: string): void {
    console.log('[AuthStateManager] 令牌已刷新');
    
    // 发送令牌刷新事件
    this.messenger.send("tokenRefreshed", {
      accessToken: newToken
    });
  }

  /**
   * 启动令牌刷新定时器
   */
  startTokenRefreshTimer(): void {
    // 每5分钟检查一次令牌是否需要刷新
    setInterval(async () => {
      if (this.currentSession) {
        const now = new Date();
        const expiresAt = new Date(this.currentSession.expiresAt);
        const timeUntilExpiry = expiresAt.getTime() - now.getTime();
        
        // 如果令牌在10分钟内过期，尝试刷新
        if (timeUntilExpiry < 10 * 60 * 1000) {
          console.log('[AuthStateManager] 令牌即将过期，尝试刷新...');
          const refreshed = await this.handleTokenRefresh();
          
          if (!refreshed) {
            console.log('[AuthStateManager] 令牌刷新失败，用户需要重新登录');
            this.setCurrentSession(null);
          }
        }
      }
    }, 5 * 60 * 1000); // 5分钟
  }

  /**
   * 处理用户登录
   */
  async handleLogin(username: string, password: string): Promise<{
    success: boolean;
    session?: CustomSessionInfo;
    error?: string;
  }> {
    try {
      const loginResponse = await this.authIntegrator['authManager'].authenticate({
        username,
        password
      });

      if (loginResponse.success && loginResponse.sessionInfo) {
        this.setCurrentSession(loginResponse.sessionInfo);
        return {
          success: true,
          session: loginResponse.sessionInfo
        };
      } else {
        return {
          success: false,
          error: loginResponse.error || '登录失败'
        };
      }
    } catch (error) {
      console.error('[AuthStateManager] 登录错误:', error);
      return {
        success: false,
        error: '登录过程中发生错误'
      };
    }
  }

  /**
   * 处理用户登出
   */
  async handleLogout(): Promise<boolean> {
    if (!this.currentSession) {
      return true;
    }

    try {
      const success = await this.authIntegrator['authManager'].logout(
        this.currentSession.sessionId
      );
      
      if (success) {
        this.setCurrentSession(null);
      }
      
      return success;
    } catch (error) {
      console.error('[AuthStateManager] 登出错误:', error);
      // 即使登出失败，也清除本地会话
      this.setCurrentSession(null);
      return false;
    }
  }

  /**
   * 验证当前会话
   */
  async validateCurrentSession(): Promise<boolean> {
    if (!this.currentSession) {
      return false;
    }

    try {
      const validation = await this.authIntegrator['authManager'].validateToken(
        this.currentSession.token.accessToken
      );

      if (!validation.isValid) {
        // 尝试刷新令牌
        const refreshed = await this.handleTokenRefresh();
        if (!refreshed) {
          this.setCurrentSession(null);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('[AuthStateManager] 会话验证错误:', error);
      this.setCurrentSession(null);
      return false;
    }
  }

  /**
   * 获取认证统计信息
   */
  async getAuthStats(): Promise<any> {
    try {
      return await this.authIntegrator['authManager'].getAuthStats();
    } catch (error) {
      console.error('[AuthStateManager] 获取认证统计失败:', error);
      return {
        totalUsers: 0,
        activeUsers: 0,
        activeSessions: 0,
        loginAttemptsToday: 0,
        failedLoginsToday: 0
      };
    }
  }
}
