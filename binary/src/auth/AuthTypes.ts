/**
 * 自定义认证系统类型定义
 */

export enum CustomAuthType {
  Local = "local",
  Database = "database", 
  LDAP = "ldap",
  Custom = "custom"
}

export interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

export interface UserCredentials {
  username: string;
  password: string;
}

export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  expiresAt: Date;
  scope?: string[];
}

export interface CustomSessionInfo {
  AUTH_TYPE: CustomAuthType;
  user: User;
  token: AuthToken;
  sessionId: string;
  createdAt: Date;
  expiresAt: Date;
}

export interface AuthConfig {
  authType: CustomAuthType;
  tokenExpiration: number; // 秒
  refreshTokenExpiration: number; // 秒
  maxLoginAttempts: number;
  lockoutDuration: number; // 秒
  passwordPolicy: PasswordPolicy;
  enableTwoFactor?: boolean;
  ldapConfig?: LDAPConfig;
  databaseConfig?: DatabaseConfig;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAge?: number; // 天数
}

export interface LDAPConfig {
  server: string;
  port: number;
  baseDN: string;
  bindDN: string;
  bindPassword: string;
  userSearchFilter: string;
  groupSearchFilter?: string;
  useTLS: boolean;
}

export interface DatabaseConfig {
  type: 'sqlite' | 'mysql' | 'postgresql';
  host?: string;
  port?: number;
  database: string;
  username?: string;
  password?: string;
  connectionString?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
  twoFactorCode?: string;
}

export interface LoginResponse {
  success: boolean;
  user?: User;
  token?: AuthToken;
  sessionInfo?: CustomSessionInfo;
  error?: string;
  requiresTwoFactor?: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  token?: AuthToken;
  error?: string;
}

export interface LogoutRequest {
  sessionId?: string;
  allSessions?: boolean;
}

export interface AuthValidationResult {
  isValid: boolean;
  user?: User;
  error?: string;
  needsRefresh?: boolean;
}

export interface UserRegistrationRequest {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface PasswordChangeRequest {
  userId: string;
  currentPassword: string;
  newPassword: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirmRequest {
  token: string;
  newPassword: string;
}

// 认证事件类型
export enum AuthEventType {
  LOGIN_SUCCESS = "login_success",
  LOGIN_FAILED = "login_failed",
  LOGOUT = "logout",
  TOKEN_REFRESH = "token_refresh",
  PASSWORD_CHANGE = "password_change",
  ACCOUNT_LOCKED = "account_locked",
  ACCOUNT_UNLOCKED = "account_unlocked",
  TWO_FACTOR_ENABLED = "two_factor_enabled",
  TWO_FACTOR_DISABLED = "two_factor_disabled"
}

export interface AuthEvent {
  type: AuthEventType;
  userId?: string;
  username?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  details?: Record<string, any>;
}

// 权限和角色
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isDefault?: boolean;
}

// 会话管理
export interface Session {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  createdAt: Date;
  expiresAt: Date;
  lastAccessAt: Date;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
}

// 安全配置
export interface SecurityConfig {
  jwtSecret: string;
  jwtAlgorithm: string;
  bcryptRounds: number;
  sessionTimeout: number;
  maxConcurrentSessions: number;
  enableAuditLog: boolean;
  enableRateLimiting: boolean;
  rateLimitWindow: number;
  rateLimitMax: number;
}

// 认证提供商接口
export interface IAuthProvider {
  authenticate(credentials: UserCredentials): Promise<LoginResponse>;
  validateToken(token: string): Promise<AuthValidationResult>;
  refreshToken(refreshToken: string): Promise<RefreshTokenResponse>;
  logout(sessionId: string): Promise<boolean>;
  changePassword(request: PasswordChangeRequest): Promise<boolean>;
  resetPassword(request: PasswordResetRequest): Promise<boolean>;
  confirmPasswordReset(request: PasswordResetConfirmRequest): Promise<boolean>;
}

// 用户存储接口
export interface IUserStorage {
  createUser(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User>;
  getUserById(id: string): Promise<User | null>;
  getUserByUsername(username: string): Promise<User | null>;
  getUserByEmail(email: string): Promise<User | null>;
  updateUser(id: string, updates: Partial<User>): Promise<User>;
  deleteUser(id: string): Promise<boolean>;
  listUsers(offset?: number, limit?: number): Promise<User[]>;
  setUserPassword(userId: string, hashedPassword: string): Promise<boolean>;
  getUserPassword(userId: string): Promise<string | null>;
}

// 会话存储接口
export interface ISessionStorage {
  createSession(session: Omit<Session, 'id'>): Promise<Session>;
  getSession(id: string): Promise<Session | null>;
  getSessionByToken(token: string): Promise<Session | null>;
  updateSession(id: string, updates: Partial<Session>): Promise<Session>;
  deleteSession(id: string): Promise<boolean>;
  deleteUserSessions(userId: string): Promise<boolean>;
  cleanupExpiredSessions(): Promise<number>;
  getUserActiveSessions(userId: string): Promise<Session[]>;
}

// 审计日志接口
export interface IAuditLogger {
  logEvent(event: AuthEvent): Promise<void>;
  getEvents(userId?: string, startDate?: Date, endDate?: Date): Promise<AuthEvent[]>;
  cleanupOldEvents(olderThan: Date): Promise<number>;
}
