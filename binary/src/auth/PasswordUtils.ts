/**
 * 密码加密和验证工具
 */

import * as crypto from 'crypto';
import { PasswordPolicy } from './AuthTypes';

export class PasswordUtils {
  private static readonly SALT_ROUNDS = 12;
  private static readonly PEPPER = process.env.PASSWORD_PEPPER || 'continue-auth-pepper-2024';

  /**
   * 生成密码哈希
   */
  static async hashPassword(password: string): Promise<string> {
    try {
      // 添加pepper增强安全性
      const pepperedPassword = password + this.PEPPER;
      
      // 生成随机盐
      const salt = crypto.randomBytes(32).toString('hex');
      
      // 使用PBKDF2进行哈希
      const hash = crypto.pbkdf2Sync(pepperedPassword, salt, 100000, 64, 'sha512').toString('hex');
      
      // 返回格式: algorithm$iterations$salt$hash
      return `pbkdf2_sha512$100000$${salt}$${hash}`;
    } catch (error) {
      throw new Error(`密码哈希失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证密码
   */
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      const parts = hashedPassword.split('$');
      if (parts.length !== 4) {
        throw new Error('无效的密码哈希格式');
      }

      const [algorithm, iterations, salt, hash] = parts;
      
      if (algorithm !== 'pbkdf2_sha512') {
        throw new Error('不支持的哈希算法');
      }

      // 添加pepper
      const pepperedPassword = password + this.PEPPER;
      
      // 使用相同的参数重新计算哈希
      const computedHash = crypto.pbkdf2Sync(
        pepperedPassword, 
        salt, 
        parseInt(iterations), 
        64, 
        'sha512'
      ).toString('hex');

      // 使用时间安全的比较
      return this.timingSafeEqual(hash, computedHash);
    } catch (error) {
      console.error('密码验证错误:', error);
      return false;
    }
  }

  /**
   * 时间安全的字符串比较
   */
  private static timingSafeEqual(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string, policy: PasswordPolicy): {
    isValid: boolean;
    errors: string[];
    score: number;
  } {
    const errors: string[] = [];
    let score = 0;

    // 检查最小长度
    if (password.length < policy.minLength) {
      errors.push(`密码长度至少需要${policy.minLength}个字符`);
    } else {
      score += 20;
    }

    // 检查大写字母
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('密码必须包含至少一个大写字母');
    } else if (/[A-Z]/.test(password)) {
      score += 15;
    }

    // 检查小写字母
    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('密码必须包含至少一个小写字母');
    } else if (/[a-z]/.test(password)) {
      score += 15;
    }

    // 检查数字
    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('密码必须包含至少一个数字');
    } else if (/\d/.test(password)) {
      score += 15;
    }

    // 检查特殊字符
    if (policy.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('密码必须包含至少一个特殊字符');
    } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 15;
    }

    // 额外的强度检查
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;

    // 检查常见弱密码模式
    const weakPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /admin/i,
      /letmein/i
    ];

    for (const pattern of weakPatterns) {
      if (pattern.test(password)) {
        errors.push('密码包含常见的弱密码模式');
        score -= 20;
        break;
      }
    }

    // 确保分数在0-100之间
    score = Math.max(0, Math.min(100, score));

    return {
      isValid: errors.length === 0,
      errors,
      score
    };
  }

  /**
   * 生成安全的随机密码
   */
  static generateSecurePassword(length: number = 16, includeSymbols: boolean = true): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    let charset = lowercase + uppercase + numbers;
    if (includeSymbols) {
      charset += symbols;
    }

    let password = '';
    
    // 确保至少包含每种类型的字符
    password += lowercase[crypto.randomInt(lowercase.length)];
    password += uppercase[crypto.randomInt(uppercase.length)];
    password += numbers[crypto.randomInt(numbers.length)];
    if (includeSymbols) {
      password += symbols[crypto.randomInt(symbols.length)];
    }

    // 填充剩余长度
    for (let i = password.length; i < length; i++) {
      password += charset[crypto.randomInt(charset.length)];
    }

    // 打乱字符顺序
    return password.split('').sort(() => crypto.randomInt(3) - 1).join('');
  }

  /**
   * 生成密码重置令牌
   */
  static generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成会话令牌
   */
  static generateSessionToken(): string {
    return crypto.randomBytes(48).toString('base64url');
  }

  /**
   * 生成API密钥
   */
  static generateApiKey(): string {
    const prefix = 'ck_';
    const randomPart = crypto.randomBytes(32).toString('hex');
    return prefix + randomPart;
  }

  /**
   * 计算密码熵
   */
  static calculatePasswordEntropy(password: string): number {
    let charset = 0;
    
    if (/[a-z]/.test(password)) charset += 26;
    if (/[A-Z]/.test(password)) charset += 26;
    if (/\d/.test(password)) charset += 10;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) charset += 32;

    return Math.log2(Math.pow(charset, password.length));
  }

  /**
   * 检查密码是否已泄露
   */
  static async checkPasswordBreach(password: string): Promise<boolean> {
    // 简单的本地检查常见泄露密码
    const commonBreachedPasswords = [
      '123456', 'password', '123456789', '12345678', '12345',
      '111111', '1234567', 'sunshine', 'qwerty', 'iloveyou',
      'princess', 'admin', 'welcome', '666666', 'abc123'
    ];

    return commonBreachedPasswords.includes(password.toLowerCase());
  }
}
