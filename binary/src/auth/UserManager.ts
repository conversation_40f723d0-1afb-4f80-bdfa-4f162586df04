/**
 * 用户管理器
 */

import { 
  User, 
  UserCredentials, 
  UserRegistrationRequest, 
  PasswordChangeRequest,
  PasswordResetRequest,
  PasswordResetConfirmRequest,
  AuthConfig,
  AuthEvent,
  AuthEventType,
  IUserStorage,
  IAuditLogger
} from './AuthTypes';
import { PasswordUtils } from './PasswordUtils';

export class UserManager {
  constructor(
    private userStorage: IUserStorage,
    private auditLogger: IAuditLogger,
    private config: AuthConfig
  ) {}

  /**
   * 创建新用户
   */
  async createUser(request: UserRegistrationRequest, createdBy?: string): Promise<User> {
    // 验证用户名是否已存在
    const existingUserByUsername = await this.userStorage.getUserByUsername(request.username);
    if (existingUserByUsername) {
      throw new Error('用户名已存在');
    }

    // 验证邮箱是否已存在
    const existingUserByEmail = await this.userStorage.getUserByEmail(request.email);
    if (existingUserByEmail) {
      throw new Error('邮箱已存在');
    }

    // 验证密码强度
    const passwordValidation = PasswordUtils.validatePasswordStrength(request.password, this.config.passwordPolicy);
    if (!passwordValidation.isValid) {
      throw new Error(`密码不符合要求: ${passwordValidation.errors.join(', ')}`);
    }

    // 检查密码是否已泄露
    const isBreached = await PasswordUtils.checkPasswordBreach(request.password);
    if (isBreached) {
      throw new Error('该密码已在数据泄露中出现，请选择其他密码');
    }

    // 加密密码
    const hashedPassword = await PasswordUtils.hashPassword(request.password);

    // 创建用户
    const userData = {
      username: request.username,
      email: request.email,
      firstName: request.firstName,
      lastName: request.lastName,
      roles: ['user'], // 默认角色
      isActive: true
    };

    const user = await this.userStorage.createUser(userData);
    
    // 保存密码
    await this.userStorage.setUserPassword(user.id, hashedPassword);

    // 记录审计日志
    await this.auditLogger.logEvent({
      type: AuthEventType.LOGIN_SUCCESS,
      userId: user.id,
      username: user.username,
      timestamp: new Date(),
      details: { createdBy }
    });

    return user;
  }

  /**
   * 验证用户凭据
   */
  async validateCredentials(credentials: UserCredentials): Promise<{ isValid: boolean; user?: User; error?: string }> {
    try {
      // 查找用户
      const user = await this.userStorage.getUserByUsername(credentials.username);
      if (!user) {
        await this.logFailedLogin(credentials.username, '用户不存在');
        return { isValid: false, error: '用户名或密码错误' };
      }

      // 检查用户是否激活
      if (!user.isActive) {
        await this.logFailedLogin(credentials.username, '用户已被禁用');
        return { isValid: false, error: '账户已被禁用' };
      }

      // 获取密码哈希
      const hashedPassword = await this.userStorage.getUserPassword(user.id);
      if (!hashedPassword) {
        await this.logFailedLogin(credentials.username, '密码未设置');
        return { isValid: false, error: '账户配置错误' };
      }

      // 验证密码
      const isPasswordValid = await PasswordUtils.verifyPassword(credentials.password, hashedPassword);
      if (!isPasswordValid) {
        await this.logFailedLogin(credentials.username, '密码错误');
        return { isValid: false, error: '用户名或密码错误' };
      }

      // 更新最后登录时间
      await this.userStorage.updateUser(user.id, { lastLoginAt: new Date() });

      // 记录成功登录
      await this.auditLogger.logEvent({
        type: AuthEventType.LOGIN_SUCCESS,
        userId: user.id,
        username: user.username,
        timestamp: new Date()
      });

      return { isValid: true, user };
    } catch (error) {
      await this.logFailedLogin(credentials.username, error.message);
      return { isValid: false, error: '登录验证失败' };
    }
  }

  /**
   * 更改密码
   */
  async changePassword(request: PasswordChangeRequest): Promise<boolean> {
    try {
      // 获取用户
      const user = await this.userStorage.getUserById(request.userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证当前密码
      const currentHashedPassword = await this.userStorage.getUserPassword(user.id);
      if (!currentHashedPassword) {
        throw new Error('当前密码未设置');
      }

      const isCurrentPasswordValid = await PasswordUtils.verifyPassword(request.currentPassword, currentHashedPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('当前密码错误');
      }

      // 验证新密码强度
      const passwordValidation = PasswordUtils.validatePasswordStrength(request.newPassword, this.config.passwordPolicy);
      if (!passwordValidation.isValid) {
        throw new Error(`新密码不符合要求: ${passwordValidation.errors.join(', ')}`);
      }

      // 检查新密码是否与当前密码相同
      const isSamePassword = await PasswordUtils.verifyPassword(request.newPassword, currentHashedPassword);
      if (isSamePassword) {
        throw new Error('新密码不能与当前密码相同');
      }

      // 加密新密码
      const newHashedPassword = await PasswordUtils.hashPassword(request.newPassword);

      // 更新密码
      await this.userStorage.setUserPassword(user.id, newHashedPassword);

      // 记录审计日志
      await this.auditLogger.logEvent({
        type: AuthEventType.PASSWORD_CHANGE,
        userId: user.id,
        username: user.username,
        timestamp: new Date()
      });

      return true;
    } catch (error) {
      console.error('密码更改失败:', error);
      return false;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserById(id: string): Promise<User | null> {
    return await this.userStorage.getUserById(id);
  }

  /**
   * 获取用户信息（通过用户名）
   */
  async getUserByUsername(username: string): Promise<User | null> {
    return await this.userStorage.getUserByUsername(username);
  }

  /**
   * 更新用户信息
   */
  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    // 如果更新邮箱，检查是否已存在
    if (updates.email) {
      const existingUser = await this.userStorage.getUserByEmail(updates.email);
      if (existingUser && existingUser.id !== id) {
        throw new Error('邮箱已被其他用户使用');
      }
    }

    // 如果更新用户名，检查是否已存在
    if (updates.username) {
      const existingUser = await this.userStorage.getUserByUsername(updates.username);
      if (existingUser && existingUser.id !== id) {
        throw new Error('用户名已被其他用户使用');
      }
    }

    return await this.userStorage.updateUser(id, updates);
  }

  /**
   * 禁用用户
   */
  async disableUser(id: string, disabledBy?: string): Promise<boolean> {
    try {
      const user = await this.userStorage.getUserById(id);
      if (!user) {
        return false;
      }

      await this.userStorage.updateUser(id, { isActive: false });

      // 记录审计日志
      await this.auditLogger.logEvent({
        type: AuthEventType.ACCOUNT_LOCKED,
        userId: user.id,
        username: user.username,
        timestamp: new Date(),
        details: { disabledBy }
      });

      return true;
    } catch (error) {
      console.error('禁用用户失败:', error);
      return false;
    }
  }

  /**
   * 启用用户
   */
  async enableUser(id: string, enabledBy?: string): Promise<boolean> {
    try {
      const user = await this.userStorage.getUserById(id);
      if (!user) {
        return false;
      }

      await this.userStorage.updateUser(id, { isActive: true });

      // 记录审计日志
      await this.auditLogger.logEvent({
        type: AuthEventType.ACCOUNT_UNLOCKED,
        userId: user.id,
        username: user.username,
        timestamp: new Date(),
        details: { enabledBy }
      });

      return true;
    } catch (error) {
      console.error('启用用户失败:', error);
      return false;
    }
  }

  /**
   * 获取用户列表
   */
  async listUsers(offset: number = 0, limit: number = 50): Promise<User[]> {
    return await this.userStorage.listUsers(offset, limit);
  }

  /**
   * 记录失败的登录尝试
   */
  private async logFailedLogin(username: string, reason: string): Promise<void> {
    await this.auditLogger.logEvent({
      type: AuthEventType.LOGIN_FAILED,
      username,
      timestamp: new Date(),
      details: { reason }
    });
  }

  /**
   * 生成用户统计信息
   */
  async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
  }> {
    const allUsers = await this.userStorage.listUsers(0, 10000);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    return {
      totalUsers: allUsers.length,
      activeUsers: allUsers.filter(u => u.isActive).length,
      newUsersToday: allUsers.filter(u => u.createdAt >= today).length
    };
  }
}
