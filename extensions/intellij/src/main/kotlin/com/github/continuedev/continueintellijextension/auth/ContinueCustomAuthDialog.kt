package com.github.continuedev.continueintellijextension.auth

import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPasswordField
import com.intellij.ui.components.JBTextField
import java.awt.BorderLayout
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import javax.swing.*

class ContinueCustomAuthDialog(
    private val project: Project,
    private val onAuthSuccess: (Map<String, Any>) -> Unit
) : DialogWrapper(true) {

    private val usernameField = JBTextField()
    private val passwordField = JBPasswordField()
    private val statusLabel = JBLabel("")
    private var loginSuccessful = false

    init {
        init()
        title = "银海通自动认证"
        setOKButtonText("开始认证")
        setCancelButtonText("取消")

        // 添加ESC键支持
        rootPane.defaultButton = getButton(okAction)
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        val topPanel = JPanel()
        topPanel.layout = BoxLayout(topPanel, BoxLayout.Y_AXIS)

        // 标题
        topPanel.add(JBLabel("银海通账户自动登录"))
        topPanel.add(Box.createVerticalStrut(20))

        // 说明信息
        val infoLabel = JBLabel("<html><center>正在自动获取银海通账户信息...<br/><br/>系统将自动检测您的银海通登录状态<br/>并使用您的账户信息进行Continue认证</center></html>")
        infoLabel.foreground = java.awt.Color.BLUE
        infoLabel.horizontalAlignment = SwingConstants.CENTER
        topPanel.add(infoLabel)
        topPanel.add(Box.createVerticalStrut(20))

        // 隐藏用户名和密码输入框，因为我们要自动获取
        usernameField.isVisible = false
        passwordField.isVisible = false

        panel.add(topPanel, BorderLayout.CENTER)

        // 状态标签
        statusLabel.foreground = java.awt.Color.RED
        statusLabel.horizontalAlignment = SwingConstants.CENTER
        panel.add(statusLabel, BorderLayout.SOUTH)

        return panel
    }

    override fun doOKAction() {
        // 如果登录已经成功，直接关闭对话框
        if (loginSuccessful) {
            super.doOKAction()
            return
        }

        // 禁用按钮，显示加载状态
        isOKActionEnabled = false
        statusLabel.text = "正在获取银海通账户信息..."
        statusLabel.foreground = java.awt.Color.BLUE

        // 在后台线程执行自动登录
        Thread {
            try {
                val loginResult = performAutoLogin()

                SwingUtilities.invokeLater {
                    println("登录响应处理: success = ${loginResult["success"]}")
                    println("完整登录结果: $loginResult")

                    if (loginResult["success"] == true) {
                        statusLabel.text = "登录成功！"
                        statusLabel.foreground = java.awt.Color.GREEN

                        // 先调用成功回调
                        try {
                            println("准备调用认证成功回调")
                            onAuthSuccess(loginResult)
                            println("认证成功回调已执行")
                        } catch (e: Exception) {
                            println("回调执行失败: ${e.message}")
                            e.printStackTrace()
                        }

                        // 标记登录成功
                        loginSuccessful = true

                        // 延迟一点时间让用户看到成功消息，然后关闭对话框
                        javax.swing.Timer(500) {
                            println("开始关闭对话框")
                            try {
                                // 直接关闭对话框
                                close(OK_EXIT_CODE, true)
                                println("对话框已关闭")
                            } catch (e: Exception) {
                                println("关闭对话框异常: ${e.message}")
                                // 强制退出对话框
                                try {
                                    super.doOKAction()
                                } catch (e2: Exception) {
                                    dispose()
                                }
                            }
                        }.apply {
                            isRepeats = false
                            start()
                        }
                    } else {
                        val error = loginResult["error"] as? String ?: "登录失败"
                        statusLabel.text = error
                        statusLabel.foreground = java.awt.Color.RED
                        isOKActionEnabled = true
                    }
                }
            } catch (e: Exception) {
                SwingUtilities.invokeLater {
                    statusLabel.text = "登录过程中发生错误: ${e.message}"
                    statusLabel.foreground = java.awt.Color.RED
                    isOKActionEnabled = true
                }
            }
        }.start()
    }

    private fun performAutoLogin(): Map<String, Any> {
        // 自动获取银海通账户信息，参考onGetStarted的实现
        return try {
            val yinhaiUserInfo = getYinhaiUserInfo()
            if (yinhaiUserInfo["success"] == true) {
                val data = yinhaiUserInfo["data"] as? Map<*, *>
                val uid = data?.get("uid") as? String

                if (uid != null) {
                    // 获取更多用户信息
                    val userDetails = getYinhaiUserDetails(uid)
                    val realName = userDetails?.get("realName") as? String ?: "银海通用户_$uid"
                    val email = userDetails?.get("email") as? String ?: "${uid}@yinhai.com"

                    mapOf<String, Any>(
                        "success" to true,
                        "token" to mapOf<String, Any>(
                            "accessToken" to generateAccessToken(uid),
                            "refreshToken" to generateRefreshToken(uid)
                        ),
                        "user" to mapOf<String, Any>(
                            "username" to uid,
                            "email" to email,
                            "firstName" to (realName.split(" ").getOrNull(0) ?: realName),
                            "lastName" to (realName.split(" ").getOrNull(1) ?: ""),
                            "uid" to uid
                        )
                    )
                } else {
                    mapOf<String, Any>("success" to false, "error" to "无法获取银海通用户ID")
                }
            } else {
                val errorMsg = yinhaiUserInfo["msg"] as? String ?: "获取银海通账户信息失败"
                mapOf<String, Any>("success" to false, "error" to errorMsg)
            }
        } catch (e: Exception) {
            mapOf<String, Any>("success" to false, "error" to "自动登录过程中发生错误: ${e.message}")
        }
    }

    private fun getYinhaiUserInfo(): Map<String, Any> {
        // 参考onGetStarted中的实现，调用银海通API获取用户信息
        return try {
            val url = java.net.URL("http://127.0.0.1:13631/getuid")
            val connection = url.openConnection() as java.net.HttpURLConnection
            connection.requestMethod = "GET"
            connection.setRequestProperty("X-Continue-URL", "http://127.0.0.1:13631/getuid")
            connection.connectTimeout = 5000
            connection.readTimeout = 10000

            val responseCode = connection.responseCode
            if (responseCode == 200) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                println("银海通API响应: $response")

                // 解析JSON响应
                val gson = com.google.gson.Gson()
                val responseMap = gson.fromJson(response, Map::class.java) as Map<String, Any>
                responseMap
            } else {
                mapOf<String, Any>("success" to false, "msg" to "银海通服务响应错误: $responseCode")
            }
        } catch (e: Exception) {
            println("调用银海通API失败: ${e.message}")
            mapOf<String, Any>("success" to false, "msg" to "无法连接到银海通服务: ${e.message}")
        }
    }

    private fun getYinhaiUserDetails(uid: String): Map<String, Any>? {
        // 可以扩展获取更多用户详细信息
        return mapOf(
            "realName" to "银海通用户_$uid",
            "email" to "${uid}@yinhai.com"
        )
    }



    private fun generateAccessToken(username: String): String {
        // 生成基于用户名的访问令牌
        val timestamp = System.currentTimeMillis()
        val tokenData = "$username:$timestamp:yinhai"
        return java.util.Base64.getEncoder().encodeToString(tokenData.toByteArray())
    }

    private fun generateRefreshToken(username: String): String {
        // 生成基于用户名的刷新令牌
        val timestamp = System.currentTimeMillis()
        val tokenData = "refresh:$username:$timestamp:yinhai"
        return java.util.Base64.getEncoder().encodeToString(tokenData.toByteArray())
    }

    override fun doCancelAction() {
        // 确保可以正常取消
        super.doCancelAction()
    }

    override fun getPreferredFocusedComponent(): JComponent {
        return if (usernameField.text.isEmpty()) usernameField else passwordField
    }
}
