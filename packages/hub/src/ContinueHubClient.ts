import {
  AssistantUnrolled,
  Config<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Slug,
  SecretResult,
} from "@continuedev/config-yaml";
import { IContinueHubClient } from "./IContinueHubClient.js";
import { YinhaiAuthAdapter, YinhaiLoginCredentials, YinhaiLoginResponse } from "./YinhaiAuthAdapter.js";
import { YinhaiAuthAdapter, YinhaiLoginCredentials, YinhaiLoginResponse } from "./YinhaiAuthAdapter.js";

interface ContinueHubClientOptions {
  apiKey?: string;
  apiBase?: string;
  fetchOptions?: RequestInit;
  // 新增：支持基于cookie的认证
  useSessionAuth?: boolean;
  loginEndpoint?: string;
}

interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  sessionId?: string;
  error?: string;
}

export class ContinueHubClient implements IContinueHubClient {
  private readonly apiKey?: string;
  private readonly apiBase: string;
  private readonly fetchOptions?: RequestInit;
  private readonly useSessionAuth: boolean;
  private readonly yinhaiAuthAdapter: YinhaiAuthAdapter;

  constructor(options: ContinueHubClientOptions) {
    this.apiKey = options.apiKey;
    this.apiBase = options.apiBase ?? "https://api.continue.dev";
    this.fetchOptions = options.fetchOptions;
    this.useSessionAuth = options.useSessionAuth ?? false;

    // 初始化银海通认证适配器
    const loginEndpoint = options.loginEndpoint ?? "http://127.0.0.1:8081/lowcodeback/aiContinueLogin";
    this.yinhaiAuthAdapter = new YinhaiAuthAdapter(loginEndpoint);
  }

  /**
   * 登录到后端系统获取会话
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await fetch(this.loginEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": credentials.username, // 根据现有代码，使用username作为Authorization
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        return {
          success: false,
          error: `登录失败: ${response.status} ${response.statusText}`,
        };
      }

      // 提取Set-Cookie头中的JSESSIONID
      const setCookieHeader = response.headers.get("set-cookie");
      if (setCookieHeader) {
        this.parseCookies(setCookieHeader);
        const jsessionId = this.cookieJar.get("JSESSIONID");
        if (jsessionId) {
          this.sessionId = jsessionId;
          return {
            success: true,
            sessionId: jsessionId,
          };
        }
      }

      return {
        success: false,
        error: "未能获取到有效的会话ID",
      };
    } catch (error) {
      return {
        success: false,
        error: `登录请求失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 解析Cookie字符串并存储到cookieJar中
   */
  private parseCookies(setCookieHeader: string): void {
    const cookies = setCookieHeader.split(",");
    for (const cookie of cookies) {
      const parts = cookie.trim().split(";");
      const [nameValue] = parts;
      const [name, value] = nameValue.split("=");
      if (name && value) {
        this.cookieJar.set(name.trim(), value.trim());
      }
    }
  }

  /**
   * 构建Cookie字符串用于请求头
   */
  private buildCookieHeader(): string {
    const cookies: string[] = [];
    for (const [name, value] of this.cookieJar.entries()) {
      cookies.push(`${name}=${value}`);
    }
    return cookies.join("; ");
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated(): boolean {
    return this.useSessionAuth ? !!this.sessionId : !!this.apiKey;
  }

  private async request(path: string, init: RequestInit): Promise<Response> {
    const url = new URL(path, this.apiBase).toString();

    const finalInit: RequestInit = {
      ...this.fetchOptions,
      ...init,
      headers: {
        ...this.fetchOptions?.headers,
        ...init.headers,
      },
    };

    // 根据认证方式设置不同的认证头
    if (this.useSessionAuth) {
      // 使用基于Cookie的会话认证
      if (this.sessionId) {
        const cookieHeader = this.buildCookieHeader();
        if (cookieHeader) {
          finalInit.headers = {
            ...finalInit.headers,
            Cookie: cookieHeader,
          };
        }
      }
    } else if (this.apiKey) {
      // 使用传统的Bearer token认证
      finalInit.headers = {
        ...finalInit.headers,
        Authorization: `Bearer ${this.apiKey}`,
      };
    }

    const resp = await fetch(url, finalInit);

    if (!resp.ok) {
      throw new Error(
        `Control plane request failed: ${resp.status} ${await resp.text()}`,
      );
    }

    return resp;
  }

  async resolveFQSNs(
    fqsns: FQSN[],
    orgScopeId: string | null,
  ): Promise<(SecretResult | undefined)[]> {
    const resp = await this.request("ide/sync-secrets", {
      method: "POST",
      body: JSON.stringify({ fqsns, orgScopeId }),
    });
    return (await resp.json()) as any;
  }
  async listAssistants(options: {
    organizationId: string | null;
    alwaysUseProxy?: boolean;
  }): Promise<
    {
      configResult: ConfigResult<AssistantUnrolled>;
      ownerSlug: string;
      packageSlug: string;
      iconUrl: string;
      rawYaml: string;
    }[]
  > {
    const organizationId = options.organizationId;
    const alwaysUseProxy = options.alwaysUseProxy ?? false;

    try {
      const urlObj = new URL(
        organizationId ? "ide/list-assistants" : "ide/list-assistants",
        this.apiBase,
      );
      if (organizationId) {
        urlObj.searchParams.set("organizationId", organizationId);
      }
      if (alwaysUseProxy) {
        urlObj.searchParams.set("alwaysUseProxy", alwaysUseProxy.toString());
      }
      const url = urlObj.toString();

      const resp = await this.request(url, {
        method: "GET",
      });
      return (await resp.json()) as any;
    } catch (e) {
      return [];
    }
  }

  async listAssistantFullSlugs(
    organizationId: string | null,
  ): Promise<FullSlug[] | null> {
    const url = organizationId
      ? `ide/list-assistant-full-slugs?organizationId=${organizationId}`
      : "ide/list-assistant-full-slugs";

    try {
      const resp = await this.request(url, {
        method: "GET",
      });
      const { fullSlugs } = (await resp.json()) as any;
      return fullSlugs;
    } catch (e) {
      return null;
    }
  }
}
