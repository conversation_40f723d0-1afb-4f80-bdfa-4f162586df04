import {
  AssistantUnrolled,
  Config<PERSON><PERSON><PERSON>,
  FQSN,
  FullSlug,
  SecretResult,
} from "@continuedev/config-yaml";

interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  sessionId?: string;
  error?: string;
}

/**
 * Interface for the Continue Hub client.
 */
export interface IContinueHubClient {
  /**
   * 登录到后端系统获取会话
   */
  login?(credentials: LoginCredentials): Promise<LoginResponse>;

  /**
   * 使用银海通账户信息自动登录
   */
  autoLoginWithYinhaiAccount?(username?: string): Promise<LoginResponse>;

  /**
   * 检查是否已认证
   */
  isAuthenticated?(): boolean;

  /**
   * 获取当前用户信息
   */
  getUserInfo?(): any;

  /**
   * 清除认证信息
   */
  clearAuth?(): void;

  resolveFQSNs(
    fqsns: FQSN[],
    orgScopeId: string | null,
  ): Promise<(SecretResult | undefined)[]>;

  /**
   * Do a full reload of all assistants used in the organization by the user.
   */
  listAssistants(options: {
    organizationId: string | null;
    alwaysUseProxy?: boolean;
  }): Promise<
    {
      configResult: ConfigResult<AssistantUnrolled>;
      ownerSlug: string;
      packageSlug: string;
      iconUrl: string;
      rawYaml: string;
    }[]
  >;

  /**
   * Get the list of FullSlugs (ownerSlug/packageSlug@versionSlug) for all assistant uses in the organization for the user.
   * Can be used to poll for changes to assistants and then full reload when needed.
   */
  listAssistantFullSlugs(
    organizationId: string | null,
  ): Promise<FullSlug[] | null>;
}
