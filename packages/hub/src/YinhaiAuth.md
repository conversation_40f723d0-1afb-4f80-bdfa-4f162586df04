# 银海通认证系统集成

本文档说明如何在Continue项目中集成银海通的基于Cookie的认证系统。

## 概述

银海通认证系统使用基于Cookie的会话认证，与Continue原有的JWT token认证方式不同。主要特点：

- **用户ID获取**: 先访问 `http://127.0.0.1:13631/getuid` 获取用户ID
- **登录接口**: `http://127.0.0.1:8081/lowcodeback/aiContinueLogin`
- **认证方式**: 在请求头中添加 `Authorization: <uid>`，然后通过响应头中的`Set-Cookie: JSESSIONID`获取会话ID
- **后续请求**: 在Cookie中携带JSESSIONID进行认证
- **自动化**: 无需手动输入用户名密码，直接使用银海通系统的用户ID

## 核心组件

### 1. YinhaiAuthAdapter

银海通认证适配器，负责处理登录逻辑和Cookie管理。

```typescript
import { YinhaiAuthAdapter } from "@continuedev/hub";

const authAdapter = new YinhaiAuthAdapter();

// 登录
const result = await authAdapter.login({
  username: "your-username",
  password: "YInhai123"
});

if (result.success) {
  console.log("登录成功，会话ID:", result.sessionId);
}
```

### 2. ContinueHubClient (增强版)

增强的Hub客户端，支持银海通认证。

```typescript
import { ContinueHubClient } from "@continuedev/hub";

const client = new ContinueHubClient({
  apiBase: "http://127.0.0.1:8081",
  useSessionAuth: true, // 启用会话认证
  loginEndpoint: "http://127.0.0.1:8081/lowcodeback/aiContinueLogin"
});
```

## 使用方法

### 基本登录流程

```typescript
// 1. 创建客户端
const client = new ContinueHubClient({
  apiBase: "http://127.0.0.1:8081",
  useSessionAuth: true
});

// 2. 登录
const loginResult = await client.login({
  username: "your-username",
  password: "YInhai123"
});

if (loginResult.success) {
  // 3. 调用需要认证的API
  const assistants = await client.listAssistants({
    organizationId: null
  });
  
  console.log("获取到的助手:", assistants);
}
```

### 自动登录（推荐）

使用银海通系统自动获取用户ID并登录，无需手动输入任何信息：

```typescript
// 自动登录，直接从银海通系统获取用户ID
const autoLoginResult = await client.autoLoginWithYinhaiAccount();

if (autoLoginResult.success) {
  console.log("自动登录成功");
}
```

### 认证状态管理

```typescript
// 检查认证状态
if (client.isAuthenticated()) {
  console.log("已认证，可以调用API");
} else {
  console.log("未认证，需要先登录");
}

// 获取用户信息
const userInfo = client.getUserInfo();
console.log("当前用户:", userInfo);

// 清除认证信息
client.clearAuth();
```

## API接口适配

所有需要认证的API调用都会自动携带JSESSIONID Cookie：

```typescript
// 这些调用会自动携带认证Cookie
await client.listAssistants({ organizationId: null });
await client.resolveFQSNs(fqsns, orgScopeId);
await client.listAssistantFullSlugs(organizationId);
```

## 错误处理

```typescript
try {
  const result = await client.login(credentials);
  
  if (!result.success) {
    console.error("登录失败:", result.error);
    
    // 尝试自动登录
    const autoResult = await client.autoLoginWithYinhaiAccount(username);
    if (autoResult.success) {
      console.log("自动登录成功");
    }
  }
} catch (error) {
  console.error("请求异常:", error);
}
```

## 与现有系统集成

### 在IntelliJ扩展中使用

```kotlin
// 在Kotlin代码中调用
val client = ContinueHubClient(mapOf(
    "apiBase" to "http://127.0.0.1:8081",
    "useSessionAuth" to true
))

// 使用银海通账户信息登录
val username = getYinhaiUsername() // 从银海通系统获取用户名
val loginResult = client.autoLoginWithYinhaiAccount(username)
```

### 在GUI中使用

```typescript
// 在React组件中使用
const authContext = useAuth();

const handleYinhaiLogin = async () => {
  const client = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081",
    useSessionAuth: true
  });
  
  const result = await client.autoLoginWithYinhaiAccount(username);
  
  if (result.success) {
    authContext.setSession({
      accessToken: result.sessionId,
      account: {
        id: username,
        label: userDisplayName
      }
    });
  }
};
```

## 配置选项

### ContinueHubClientOptions

```typescript
interface ContinueHubClientOptions {
  apiKey?: string;           // 传统API密钥（可选）
  apiBase?: string;          // API基础URL
  fetchOptions?: RequestInit; // 额外的fetch选项
  useSessionAuth?: boolean;   // 是否使用会话认证
  loginEndpoint?: string;     // 登录接口URL
}
```

### 默认配置

```typescript
const defaultOptions = {
  apiBase: "https://api.continue.dev",
  useSessionAuth: false,
  loginEndpoint: "http://127.0.0.1:8081/lowcodeback/aiContinueLogin"
};
```

## 调试和日志

认证适配器包含详细的日志输出：

```
🔐 开始银海通登录流程...
📡 登录请求响应状态: 200
🍪 Set-Cookie 头: JSESSIONID=15159772B6E2EBB723DE152C4AEC1095; Path=/lowcodeback; HttpOnly
🍪 解析Cookie: JSESSIONID=15159772B6...
✅ 成功获取 JSESSIONID: 15159772B6...
👤 获取用户信息: 用户显示名
```

## 注意事项

1. **网络环境**: 确保能够访问`http://127.0.0.1:8081`
2. **Cookie安全**: JSESSIONID包含HttpOnly标志，增强安全性
3. **会话过期**: 需要处理会话过期的情况，可能需要重新登录
4. **并发请求**: Cookie会自动在所有请求中携带
5. **跨域问题**: 如果前端和后端不在同一域，需要配置CORS

## 迁移指南

从传统认证迁移到银海通认证：

1. **更新客户端创建**:
   ```typescript
   // 旧方式
   const client = new ContinueHubClient({
     apiKey: "your-api-key"
   });
   
   // 新方式
   const client = new ContinueHubClient({
     apiBase: "http://127.0.0.1:8081",
     useSessionAuth: true
   });
   ```

2. **添加登录流程**:
   ```typescript
   // 在使用API之前先登录
   await client.autoLoginWithYinhaiAccount(username);
   ```

3. **更新错误处理**:
   ```typescript
   // 处理认证失败的情况
   if (!client.isAuthenticated()) {
     await client.autoLoginWithYinhaiAccount(username);
   }
   ```

## 示例代码

完整的使用示例请参考 `examples/YinhaiAuthExample.ts` 文件。
