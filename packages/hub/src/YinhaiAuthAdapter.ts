/**
 * 银海通认证适配器
 * 处理银海通登录接口的认证逻辑
 */

interface YinhaiLoginCredentials {
  username: string;
  password: string;
}

interface YinhaiLoginResponse {
  success: boolean;
  sessionId?: string;
  error?: string;
  userInfo?: {
    username: string;
    displayName: string;
    uid: string;
  };
}

interface YinhaiUserInfo {
  uid: string;
  username: string;
  displayName: string;
  [key: string]: any;
}

export class YinhaiAuthAdapter {
  private readonly loginEndpoint: string;
  private sessionId?: string;
  private userInfo?: YinhaiUserInfo;
  private cookieJar: Map<string, string> = new Map();

  constructor(loginEndpoint: string = "http://127.0.0.1:8081/lowcodeback/aiContinueLogin") {
    this.loginEndpoint = loginEndpoint;
  }

  /**
   * 获取银海通用户ID
   */
  private async getYinhaiUid(): Promise<string | null> {
    try {
      console.log("🔍 获取银海通用户ID...");

      const response = await fetch("http://127.0.0.1:13631/getuid", {
        method: "GET",
        headers: {
          "X-Continue-URL": "http://127.0.0.1:13631/getuid",
        },
      });

      if (!response.ok) {
        console.error(`❌ 获取用户ID失败: ${response.status} ${response.statusText}`);
        return null;
      }

      const responseText = await response.text();
      console.log(`📋 获取到用户ID: ${responseText}`);

      return responseText.trim();
    } catch (error) {
      console.error("❌ 获取用户ID异常:", error);
      return null;
    }
  }

  /**
   * 登录到银海通系统
   */
  async login(credentials: YinhaiLoginCredentials): Promise<YinhaiLoginResponse> {
    try {
      console.log("🔐 开始银海通登录流程...");

      // 1. 先获取银海通用户ID
      const uid = await this.getYinhaiUid();
      if (!uid) {
        return {
          success: false,
          error: "无法获取银海通用户ID",
        };
      }

      console.log(`🎫 使用用户ID进行登录: ${uid}`);

      // 2. 使用获取到的uid作为Authorization header调用登录接口
      const response = await fetch(this.loginEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": uid, // 使用从getuid接口获取的uid
        },
        // 登录接口可能不需要body，但保留以防万一
        body: JSON.stringify({
          username: credentials.username,
          password: credentials.password,
        }),
      });

      console.log(`📡 登录请求响应状态: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ 登录请求失败: ${response.status} ${errorText}`);
        return {
          success: false,
          error: `登录失败: ${response.status} ${response.statusText}`,
        };
      }

      // 提取Set-Cookie头中的JSESSIONID
      const setCookieHeader = response.headers.get("set-cookie");
      console.log(`🍪 Set-Cookie 头: ${setCookieHeader}`);

      if (setCookieHeader) {
        this.parseCookies(setCookieHeader);
        const jsessionId = this.cookieJar.get("JSESSIONID");

        if (jsessionId) {
          this.sessionId = jsessionId;
          console.log(`✅ 成功获取 JSESSIONID: ${jsessionId.substring(0, 10)}...`);

          // 设置用户信息
          this.userInfo = {
            uid: uid,
            username: credentials.username,
            displayName: credentials.username,
          };

          // 尝试解析响应体获取更多用户信息
          try {
            const responseData = await response.json();
            if (responseData) {
              this.userInfo = {
                ...this.userInfo,
                ...responseData,
                uid: uid, // 确保使用正确的uid
              };
              console.log(`👤 获取用户信息: ${this.userInfo.displayName}`);
            }
          } catch (e) {
            console.warn("⚠️ 无法解析响应体中的用户信息，使用默认信息");
          }

          return {
            success: true,
            sessionId: jsessionId,
            userInfo: {
              username: this.userInfo.username,
              displayName: this.userInfo.displayName,
              uid: this.userInfo.uid,
            },
          };
        }
      }

      console.error("❌ 未能从响应中获取到有效的JSESSIONID");
      return {
        success: false,
        error: "未能获取到有效的会话ID",
      };
    } catch (error) {
      console.error("❌ 登录请求异常:", error);
      return {
        success: false,
        error: `登录请求失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 解析Cookie字符串并存储到cookieJar中
   */
  private parseCookies(setCookieHeader: string): void {
    // 处理多个Set-Cookie头的情况
    const cookieStrings = setCookieHeader.includes(",") 
      ? setCookieHeader.split(",").map(s => s.trim())
      : [setCookieHeader];

    for (const cookieString of cookieStrings) {
      const parts = cookieString.split(";");
      const [nameValue] = parts;
      const [name, value] = nameValue.split("=");
      
      if (name && value) {
        const cookieName = name.trim();
        const cookieValue = value.trim();
        this.cookieJar.set(cookieName, cookieValue);
        console.log(`🍪 解析Cookie: ${cookieName}=${cookieValue.substring(0, 10)}...`);
      }
    }
  }

  /**
   * 构建Cookie字符串用于请求头
   */
  buildCookieHeader(): string {
    const cookies: string[] = [];
    for (const [name, value] of this.cookieJar.entries()) {
      cookies.push(`${name}=${value}`);
    }
    return cookies.join("; ");
  }

  /**
   * 获取当前会话ID
   */
  getSessionId(): string | undefined {
    return this.sessionId;
  }

  /**
   * 获取当前用户信息
   */
  getUserInfo(): YinhaiUserInfo | undefined {
    return this.userInfo;
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated(): boolean {
    return !!this.sessionId;
  }

  /**
   * 清除认证信息
   */
  clearAuth(): void {
    this.sessionId = undefined;
    this.userInfo = undefined;
    this.cookieJar.clear();
    console.log("🧹 已清除认证信息");
  }

  /**
   * 获取所有Cookie
   */
  getCookies(): Map<string, string> {
    return new Map(this.cookieJar);
  }

  /**
   * 设置Cookie
   */
  setCookie(name: string, value: string): void {
    this.cookieJar.set(name, value);
  }

  /**
   * 从银海通获取用户账户信息（模拟onGetStarted中的获取方式）
   */
  async getYinhaiAccountInfo(): Promise<YinhaiUserInfo | null> {
    try {
      // 这里应该调用银海通的用户信息接口
      // 目前返回已存储的用户信息
      return this.userInfo || null;
    } catch (error) {
      console.error("❌ 获取银海通账户信息失败:", error);
      return null;
    }
  }

  /**
   * 使用统一密码YInhai123进行自动登录
   */
  async autoLoginWithYinhaiAccount(username: string): Promise<YinhaiLoginResponse> {
    const defaultPassword = "YInhai123";
    console.log(`🔄 使用统一密码为用户 ${username} 进行自动登录`);
    
    return await this.login({
      username,
      password: defaultPassword,
    });
  }
}

export type { YinhaiLoginCredentials, YinhaiLoginResponse, YinhaiUserInfo };
