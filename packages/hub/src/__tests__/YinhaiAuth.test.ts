/**
 * 银海通认证系统测试
 */

import { YinhaiAuthAdapter } from "../YinhaiAuthAdapter.js";
import { ContinueHubClient } from "../ContinueHubClient.js";

// Mock fetch for testing
global.fetch = jest.fn();

describe("YinhaiAuthAdapter", () => {
  let authAdapter: YinhaiAuthAdapter;

  beforeEach(() => {
    authAdapter = new YinhaiAuthAdapter();
    jest.clearAllMocks();
  });

  describe("login", () => {
    it("should successfully login with valid credentials", async () => {
      // Mock successful login response
      const mockResponse = {
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue("JSESSIONID=test-session-id; Path=/lowcodeback; HttpOnly"),
        },
        json: jest.fn().mockResolvedValue({
          uid: "test-uid",
          displayName: "Test User",
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await authAdapter.login({
        username: "testuser",
        password: "YInhai123",
      });

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe("test-session-id");
      expect(result.userInfo?.username).toBe("testuser");
      expect(result.userInfo?.uid).toBe("test-uid");
    });

    it("should handle login failure", async () => {
      // Mock failed login response
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: "Unauthorized",
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await authAdapter.login({
        username: "wronguser",
        password: "wrongpassword",
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain("登录失败");
    });

    it("should handle network errors", async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error("Network error"));

      const result = await authAdapter.login({
        username: "testuser",
        password: "YInhai123",
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain("登录请求失败");
    });
  });

  describe("autoLoginWithYinhaiAccount", () => {
    it("should use default password for auto login", async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue("JSESSIONID=auto-session-id; Path=/lowcodeback; HttpOnly"),
        },
        json: jest.fn().mockResolvedValue({}),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await authAdapter.autoLoginWithYinhaiAccount("autouser");

      expect(result.success).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            username: "autouser",
            password: "YInhai123",
          }),
        })
      );
    });
  });

  describe("authentication state", () => {
    it("should track authentication state correctly", () => {
      expect(authAdapter.isAuthenticated()).toBe(false);

      // Simulate successful login by setting session ID
      authAdapter.setCookie("JSESSIONID", "test-session");

      // Note: isAuthenticated checks for sessionId, which is set during login
      // For this test, we need to simulate the full login process
    });

    it("should clear authentication state", () => {
      authAdapter.setCookie("JSESSIONID", "test-session");
      authAdapter.clearAuth();

      expect(authAdapter.isAuthenticated()).toBe(false);
      expect(authAdapter.getSessionId()).toBeUndefined();
    });
  });

  describe("cookie management", () => {
    it("should build cookie header correctly", () => {
      authAdapter.setCookie("JSESSIONID", "test-session-id");
      authAdapter.setCookie("OTHER_COOKIE", "other-value");

      const cookieHeader = authAdapter.buildCookieHeader();
      expect(cookieHeader).toContain("JSESSIONID=test-session-id");
      expect(cookieHeader).toContain("OTHER_COOKIE=other-value");
    });
  });
});

describe("ContinueHubClient with Yinhai Auth", () => {
  let client: ContinueHubClient;

  beforeEach(() => {
    client = new ContinueHubClient({
      apiBase: "http://127.0.0.1:8081",
      useSessionAuth: true,
    });
    jest.clearAllMocks();
  });

  describe("login", () => {
    it("should use YinhaiAuthAdapter for session auth", async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue("JSESSIONID=client-session-id; Path=/lowcodeback; HttpOnly"),
        },
        json: jest.fn().mockResolvedValue({}),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await client.login({
        username: "clientuser",
        password: "YInhai123",
      });

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe("client-session-id");
    });

    it("should return error for non-session auth", async () => {
      const nonSessionClient = new ContinueHubClient({
        apiKey: "test-api-key",
        useSessionAuth: false,
      });

      const result = await nonSessionClient.login({
        username: "testuser",
        password: "password",
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain("当前配置不支持登录功能");
    });
  });

  describe("autoLoginWithYinhaiAccount", () => {
    it("should perform auto login", async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue("JSESSIONID=auto-client-session; Path=/lowcodeback; HttpOnly"),
        },
        json: jest.fn().mockResolvedValue({}),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await client.autoLoginWithYinhaiAccount("autoclient");

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe("auto-client-session");
    });
  });

  describe("API requests with authentication", () => {
    it("should include cookies in authenticated requests", async () => {
      // First, simulate successful login
      const loginResponse = {
        ok: true,
        status: 200,
        headers: {
          get: jest.fn().mockReturnValue("JSESSIONID=api-session-id; Path=/lowcodeback; HttpOnly"),
        },
        json: jest.fn().mockResolvedValue({}),
      };

      const apiResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue([]),
      };

      (global.fetch as jest.Mock)
        .mockResolvedValueOnce(loginResponse) // Login call
        .mockResolvedValueOnce(apiResponse); // API call

      // Login first
      await client.login({
        username: "apiuser",
        password: "YInhai123",
      });

      // Make API call
      await client.listAssistants({ organizationId: null });

      // Check that the API call included the cookie
      expect(global.fetch).toHaveBeenCalledTimes(2);
      const apiCall = (global.fetch as jest.Mock).mock.calls[1];
      expect(apiCall[1].headers).toHaveProperty("Cookie");
      expect(apiCall[1].headers.Cookie).toContain("JSESSIONID=api-session-id");
    });

    it("should not include cookies for non-session auth", async () => {
      const nonSessionClient = new ContinueHubClient({
        apiKey: "test-api-key",
        useSessionAuth: false,
      });

      const apiResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue([]),
      };

      (global.fetch as jest.Mock).mockResolvedValue(apiResponse);

      await nonSessionClient.listAssistants({ organizationId: null });

      const apiCall = (global.fetch as jest.Mock).mock.calls[0];
      expect(apiCall[1].headers).toHaveProperty("Authorization");
      expect(apiCall[1].headers.Authorization).toBe("Bearer test-api-key");
      expect(apiCall[1].headers).not.toHaveProperty("Cookie");
    });
  });

  describe("authentication state management", () => {
    it("should track authentication state", () => {
      expect(client.isAuthenticated()).toBe(false);

      // After successful login, should be authenticated
      // This would be tested in integration tests with actual login
    });

    it("should clear authentication", () => {
      client.clearAuth();
      expect(client.isAuthenticated()).toBe(false);
    });
  });
});
