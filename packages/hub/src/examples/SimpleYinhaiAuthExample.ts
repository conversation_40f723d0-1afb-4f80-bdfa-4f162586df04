/**
 * 简化的银海通认证使用示例
 * 展示新的基于getuid接口的认证流程
 */

import { ContinueHubClient } from "../ContinueHubClient.js";

/**
 * 最简单的银海通认证示例
 */
async function simpleYinhaiAuthExample() {
  console.log("🚀 银海通认证系统 - 简化示例");

  // 1. 创建支持银海通认证的客户端
  const client = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081", // 银海通后端地址
    useSessionAuth: true, // 启用会话认证
  });

  try {
    // 2. 自动登录（推荐方式）
    // 这会自动调用 http://127.0.0.1:13631/getuid 获取用户ID
    // 然后使用该ID作为Authorization header调用登录接口
    console.log("\n🔄 开始自动登录...");
    const loginResult = await client.autoLoginWithYinhaiAccount();

    if (loginResult.success) {
      console.log("✅ 登录成功!");
      console.log(`🎫 会话ID: ${loginResult.sessionId}`);
      
      // 3. 获取用户信息
      const userInfo = client.getUserInfo();
      console.log(`👤 用户信息:`, userInfo);

      // 4. 调用需要认证的API
      console.log("\n📡 调用API接口...");
      
      try {
        // 调用list-assistants接口，会自动携带JSESSIONID Cookie
        const assistants = await client.listAssistants({
          organizationId: null,
        });
        
        console.log(`📋 成功获取 ${assistants.length} 个助手`);
        if (assistants.length > 0) {
          console.log("前3个助手:");
          assistants.slice(0, 3).forEach((assistant, index) => {
            console.log(`  ${index + 1}. ${assistant.ownerSlug}/${assistant.packageSlug}`);
          });
        }
      } catch (error) {
        console.error("❌ API调用失败:", error);
      }

    } else {
      console.error("❌ 登录失败:", loginResult.error);
      
      // 可能的失败原因：
      // 1. 无法访问 http://127.0.0.1:13631/getuid
      // 2. 无法访问 http://127.0.0.1:8081/lowcodeback/aiContinueLogin
      // 3. 网络连接问题
      console.log("\n🔍 可能的解决方案:");
      console.log("1. 确保银海通系统正在运行");
      console.log("2. 检查网络连接");
      console.log("3. 确认端口13631和8081可访问");
    }

  } catch (error) {
    console.error("❌ 认证过程异常:", error);
  } finally {
    // 5. 清理（可选）
    console.log("\n🧹 清理认证信息");
    client.clearAuth();
    console.log(`🔐 认证状态: ${client.isAuthenticated() ? "已认证" : "未认证"}`);
  }
}

/**
 * 手动登录示例（如果需要传递特定的用户名密码）
 */
async function manualLoginExample() {
  console.log("\n📝 手动登录示例");

  const client = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081",
    useSessionAuth: true,
  });

  try {
    // 手动登录，仍然会先调用getuid获取用户ID
    const loginResult = await client.login({
      username: "specific-username",
      password: "any-password", // 实际上不会使用，因为认证基于uid
    });

    if (loginResult.success) {
      console.log("✅ 手动登录成功!");
      console.log(`🎫 会话ID: ${loginResult.sessionId}`);
    } else {
      console.error("❌ 手动登录失败:", loginResult.error);
    }
  } catch (error) {
    console.error("❌ 手动登录异常:", error);
  }
}

/**
 * 认证状态检查示例
 */
async function authStatusExample() {
  console.log("\n🔍 认证状态检查示例");

  const client = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081",
    useSessionAuth: true,
  });

  // 检查初始状态
  console.log(`初始认证状态: ${client.isAuthenticated()}`);

  // 登录
  const loginResult = await client.autoLoginWithYinhaiAccount();
  
  if (loginResult.success) {
    console.log(`登录后认证状态: ${client.isAuthenticated()}`);
    
    // 获取用户信息
    const userInfo = client.getUserInfo();
    console.log("用户信息:", {
      username: userInfo?.username,
      uid: userInfo?.uid,
      displayName: userInfo?.displayName,
    });
    
    // 清除认证
    client.clearAuth();
    console.log(`清除后认证状态: ${client.isAuthenticated()}`);
  }
}

/**
 * 错误处理示例
 */
async function errorHandlingExample() {
  console.log("\n🛡️ 错误处理示例");

  const client = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081",
    useSessionAuth: true,
  });

  try {
    // 尝试登录
    const loginResult = await client.autoLoginWithYinhaiAccount();
    
    if (!loginResult.success) {
      console.log("⚠️ 登录失败，分析错误原因:");
      
      if (loginResult.error?.includes("getuid")) {
        console.log("- 可能是getuid接口无法访问");
        console.log("- 检查 http://127.0.0.1:13631/getuid 是否可用");
      } else if (loginResult.error?.includes("登录失败")) {
        console.log("- 可能是登录接口无法访问");
        console.log("- 检查 http://127.0.0.1:8081/lowcodeback/aiContinueLogin 是否可用");
      } else {
        console.log("- 其他网络或系统错误");
      }
      
      return;
    }

    // 尝试API调用
    try {
      await client.listAssistants({ organizationId: null });
      console.log("✅ API调用成功");
    } catch (apiError) {
      console.log("⚠️ API调用失败，可能需要重新认证");
      
      // 重新认证
      client.clearAuth();
      const reauth = await client.autoLoginWithYinhaiAccount();
      
      if (reauth.success) {
        console.log("✅ 重新认证成功");
        // 重试API调用
        try {
          await client.listAssistants({ organizationId: null });
          console.log("✅ 重试API调用成功");
        } catch (retryError) {
          console.error("❌ 重试仍然失败:", retryError);
        }
      }
    }
    
  } catch (error) {
    console.error("❌ 整体流程异常:", error);
  }
}

// 导出示例函数
export {
  simpleYinhaiAuthExample,
  manualLoginExample,
  authStatusExample,
  errorHandlingExample,
};

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  (async () => {
    await simpleYinhaiAuthExample();
    await manualLoginExample();
    await authStatusExample();
    await errorHandlingExample();
  })().catch(console.error);
}
