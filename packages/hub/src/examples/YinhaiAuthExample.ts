/**
 * 银海通认证系统使用示例
 * 展示如何使用新的基于Cookie的认证系统
 */

import { ContinueHubClient } from "../ContinueHubClient.js";

/**
 * 示例：使用银海通认证系统
 */
async function exampleYinhaiAuth() {
  console.log("🚀 银海通认证系统示例");

  // 1. 创建支持会话认证的客户端
  const client = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081", // 银海通后端地址
    useSessionAuth: true, // 启用会话认证
    loginEndpoint: "http://127.0.0.1:8081/lowcodeback/aiContinueLogin", // 登录接口
  });

  try {
    // 2. 方式一：手动登录
    console.log("\n📝 方式一：手动登录");
    const loginResult = await client.login({
      username: "testuser",
      password: "YInhai123", // 统一密码
    });

    if (loginResult.success) {
      console.log("✅ 登录成功!");
      console.log(`🎫 会话ID: ${loginResult.sessionId}`);
      
      // 获取用户信息
      const userInfo = client.getUserInfo();
      console.log(`👤 用户信息:`, userInfo);
    } else {
      console.error("❌ 登录失败:", loginResult.error);
    }

    // 3. 方式二：使用银海通自动登录（推荐）
    console.log("\n🔄 方式二：银海通自动登录（直接获取用户ID）");
    const autoLoginResult = await client.autoLoginWithYinhaiAccount();

    if (autoLoginResult.success) {
      console.log("✅ 自动登录成功!");
      console.log(`🎫 会话ID: ${autoLoginResult.sessionId}`);
    } else {
      console.error("❌ 自动登录失败:", autoLoginResult.error);
    }

    // 4. 检查认证状态
    console.log("\n🔍 检查认证状态");
    const isAuthenticated = client.isAuthenticated();
    console.log(`🔐 认证状态: ${isAuthenticated ? "已认证" : "未认证"}`);

    if (isAuthenticated) {
      // 5. 调用需要认证的API
      console.log("\n📡 调用API接口");
      
      try {
        // 调用list-assistants接口
        const assistants = await client.listAssistants({
          organizationId: null,
          alwaysUseProxy: false,
        });
        
        console.log(`📋 获取到 ${assistants.length} 个助手`);
        assistants.forEach((assistant, index) => {
          console.log(`  ${index + 1}. ${assistant.ownerSlug}/${assistant.packageSlug}`);
        });
      } catch (error) {
        console.error("❌ API调用失败:", error);
      }
    }

    // 6. 清除认证信息
    console.log("\n🧹 清除认证信息");
    client.clearAuth();
    console.log(`🔐 认证状态: ${client.isAuthenticated() ? "已认证" : "未认证"}`);

  } catch (error) {
    console.error("❌ 示例执行失败:", error);
  }
}

/**
 * 示例：在现有Continue系统中集成银海通认证
 */
async function exampleIntegrateWithContinue() {
  console.log("\n🔗 Continue系统集成示例");

  // 创建传统的Continue Hub客户端（用于对比）
  const traditionalClient = new ContinueHubClient({
    apiKey: "your-api-key",
    apiBase: "https://api.continue.dev",
    useSessionAuth: false, // 使用传统认证
  });

  // 创建银海通认证客户端
  const yinhaiClient = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081",
    useSessionAuth: true, // 使用银海通认证
  });

  console.log("📊 对比两种认证方式:");
  console.log(`传统认证状态: ${traditionalClient.isAuthenticated()}`);
  console.log(`银海通认证状态: ${yinhaiClient.isAuthenticated()}`);

  // 根据配置选择合适的客户端
  const activeClient = yinhaiClient.isAuthenticated() ? yinhaiClient : traditionalClient;
  console.log(`🎯 使用${yinhaiClient.isAuthenticated() ? "银海通" : "传统"}认证客户端`);

  return activeClient;
}

/**
 * 示例：错误处理和重试机制
 */
async function exampleErrorHandling() {
  console.log("\n🛡️ 错误处理示例");

  const client = new ContinueHubClient({
    apiBase: "http://127.0.0.1:8081",
    useSessionAuth: true,
  });

  // 模拟登录失败的情况
  const failedLogin = await client.login({
    username: "wronguser",
    password: "wrongpassword",
  });

  if (!failedLogin.success) {
    console.log("⚠️ 登录失败，尝试使用默认账户");
    
    // 尝试使用默认账户
    const retryLogin = await client.autoLoginWithYinhaiAccount("defaultuser");
    
    if (retryLogin.success) {
      console.log("✅ 使用默认账户登录成功");
    } else {
      console.error("❌ 所有登录尝试都失败了");
      return;
    }
  }

  // 模拟API调用失败的情况
  try {
    await client.listAssistants({ organizationId: null });
  } catch (error) {
    console.log("⚠️ API调用失败，可能需要重新认证");
    
    // 清除旧的认证信息并重新登录
    client.clearAuth();
    const reauth = await client.autoLoginWithYinhaiAccount("defaultuser");
    
    if (reauth.success) {
      console.log("✅ 重新认证成功，可以重试API调用");
    }
  }
}

// 导出示例函数
export {
  exampleYinhaiAuth,
  exampleIntegrateWithContinue,
  exampleErrorHandling,
};

// 如果直接运行此文件，执行示例
if (require.main === module) {
  (async () => {
    await exampleYinhaiAuth();
    await exampleIntegrateWithContinue();
    await exampleErrorHandling();
  })().catch(console.error);
}
